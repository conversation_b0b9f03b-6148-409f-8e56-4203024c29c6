feeder: dataset.dataloader_video.BaseFeeder
phase: train
dataset: phoenix2014
num_epoch: 80
work_dir: ./work_dir/optimized_res18_attention/
batch_size: 6  # 进一步增加批次大小
random_seed: 42  # 更换随机种子
test_batch_size: 6
num_worker: 12
device: 0
log_interval: 5000  # 更频繁的日志输出
eval_interval: 1
save_interval: 3
evaluate_tool: python

# 优化的损失权重
loss_weights:
  SeqCTC: 1.2    # 增加序列CTC权重
  ConvCTC: 0.6   # 降低卷积CTC权重
  Dist: 15.0     # 进一步降低蒸馏损失

# 优化的学习率策略
optimizer_args:
  optimizer: AdamW  # 使用AdamW优化器
  base_lr: 0.00008
  step: [ 30, 50, 70]  # 多阶段学习率衰减
  learning_ratio: 1
  weight_decay: 0.00003
  start_epoch: 0
  nesterov: False

# 数据增强参数
feeder_args:
  mode: 'train'
  datatype: 'video'
  num_gloss: -1
  drop_ratio: 0.9    # 增加dropout
  frame_interval: 1
  image_scale: 1.1   # 轻微的图像缩放增强
  input_size: 224

model: slr_network.SLRModel
decode_mode: beam
model_args:
  num_classes: 1296
  c2d_type: resnet18
  conv_type: 2
  use_bn: 1
  share_classifier: True
  weight_norm: True

# 优化的注意力参数
attention_params:
  max_window_size: 15
  kernel_sizes: [3, 5, 7, 9, 11]  # 更多尺度组合
  reduction_ratio: 4              # 更低的压缩比
  use_dynamic_weights: true       # 启用动态权重预测
