feeder: dataset.dataloader_video.BaseFeeder
phase: train
dataset: phoenix2014
# dataset: phoenix14-si5
num_epoch: 60
work_dir: ./work_dir/baseline_res18_attention_optimized/
batch_size: 4  # 增加批次大小
random_seed: 0
test_batch_size: 4
num_worker: 10
device: 0
log_interval: 10000
eval_interval: 1
save_interval: 5
# python in default
evaluate_tool: python #sclite
loss_weights:
  SeqCTC: 1.0
  # VAC
  ConvCTC: 0.8  # 降低ConvCTC权重
  Dist: 20.0    # 降低蒸馏损失权重
#load_weights: ''

optimizer_args:
  optimizer: Adam
  base_lr: 0.00005  # 降低初始学习率
  step: [ 25, 40]   # 延后学习率衰减
  learning_ratio: 1
  weight_decay: 0.00005  # 减少权重衰减
  start_epoch: 0
  nesterov: False

feeder_args:
  mode: 'train'
  datatype: 'video'
  num_gloss: -1
  drop_ratio: 1.0
  frame_interval: 1
  image_scale: 1.0  # 0-1 represents ratio, >1 represents absolute value
  input_size: 224

model: slr_network.SLRModel
decode_mode: beam
model_args:
  num_classes: 1296
  c2d_type: resnet18 #resnet18, mobilenet_v2, squeezenet1_1, shufflenet_v2_x1_0, efficientnet_b1, mnasnet1_0, regnet_y_800mf, vgg16_bn, vgg11_bn, regnet_x_800mf, regnet_x_400mf, densenet121, regnet_y_1_6gf
  conv_type: 2
  use_bn: 1
  # SMKD
  share_classifier: True
  weight_norm: True

# 动态时间注意力机制参数
attention_params:
  max_window_size: 11
  kernel_sizes: [5, 9, 13]
  reduction_ratio: 16