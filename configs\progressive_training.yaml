feeder: dataset.dataloader_video.BaseFeeder
phase: train
dataset: phoenix2014
num_epoch: 100
work_dir: ./work_dir/progressive_res18_attention/
batch_size: 8
random_seed: 123
test_batch_size: 8
num_worker: 16
device: 0
log_interval: 3000
eval_interval: 1
save_interval: 2
evaluate_tool: python

# 渐进式损失权重策略
loss_weights:
  SeqCTC: 1.5
  ConvCTC: 0.5
  Dist: 10.0

# 更激进的学习率策略
optimizer_args:
  optimizer: AdamW
  base_lr: 0.0001
  step: [ 15, 35, 55, 75, 90]  # 更细粒度的学习率调整
  learning_ratio: 1
  weight_decay: 0.00001  # 更小的权重衰减
  start_epoch: 0
  nesterov: False

feeder_args:
  mode: 'train'
  datatype: 'video'
  num_gloss: -1
  drop_ratio: 0.8
  frame_interval: 1
  image_scale: 1.2
  input_size: 256  # 增加输入分辨率

model: slr_network.SLRModel
decode_mode: beam
model_args:
  num_classes: 1296
  c2d_type: resnet18
  conv_type: 2
  use_bn: 1
  share_classifier: True
  weight_norm: True

# 最优注意力参数（基于您的调参结果）
attention_params:
  max_window_size: 11
  kernel_sizes: [5, 9, 13]
  reduction_ratio: 16
  use_dynamic_weights: true
